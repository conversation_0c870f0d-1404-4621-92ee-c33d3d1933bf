description = 'Maling Graph Vector - 向量服务'

dependencies {
    // 依赖核心模块
    api project(':maling-graph-base')
    
    // OpenAI client for vector generation
    api "com.theokanning.openai-gpt3-java:service:${openaiVersion}"
    
    // HTTP client
    implementation "org.apache.httpcomponents:httpclient:${httpclientVersion}"
    implementation "org.apache.httpcomponents:httpcore:${httpcoreVersion}"
    
    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
    
    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"
    
    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}
