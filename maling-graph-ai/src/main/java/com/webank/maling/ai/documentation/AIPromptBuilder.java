package com.webank.maling.ai.documentation;

import com.webank.maling.base.model.MethodInfo;
import com.webank.maling.base.model.SubgraphData;
import lombok.extern.slf4j.Slf4j;

import java.util.stream.Collectors;

/**
 * AI提示词构建器
 * 根据不同层级和上下文构建优化的提示词
 * 
 * <AUTHOR> Graph Team
 */
@Slf4j
public class AIPromptBuilder {
    
    /**
     * 根据层级构建提示词
     * 
     * @param context 文档生成上下文
     * @return 构建的提示词
     */
    public String buildPromptForLevel(DocumentationGenerationContext context) {
        try {
            return switch (context.getLevel()) {
                case 1 -> buildLevel1Prompt(context);
                case 2 -> buildLevel2Prompt(context);
                case 3 -> buildLevel3Prompt(context);
                default -> buildLevel3Prompt(context);
            };
        } catch (Exception e) {
            log.error("构建提示词时发生错误", e);
            return null;
        }
    }
    
    /**
     * 构建第1层（核心流程）提示词
     */
    private String buildLevel1Prompt(DocumentationGenerationContext context) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个资深的Java架构师。请基于以下代码调用图谱，生成该业务流程的核心说明书。\n\n");
        
        // 入口方法信息
        var entryPoint = context.getSubgraph().getEntryPoint();
        prompt.append("**入口方法**: ").append(entryPoint.getFullName()).append("\n");
        prompt.append("**方法签名**: ").append(entryPoint.getSignature()).append("\n\n");
        
        // 核心调用链
        prompt.append("**核心调用链**:\n");
        var level1Methods = context.getSubgraph().getMethodsAtLevel(1);
        for (var method : level1Methods) {
            prompt.append("- ").append(method.getSimpleIdentifier());
            if (method.getDescription() != null) {
                prompt.append(" (").append(method.getDescription()).append(")");
            }
            prompt.append("\n");
        }
        
        // 调用关系
        prompt.append("\n**调用关系**:\n");
        var relations = context.getSubgraph().getRelations().stream()
                .filter(r -> isLevel1Relation(r, context))
                .collect(Collectors.toList());
        
        for (var relation : relations) {
            prompt.append("- ").append(getMethodName(relation.getSourceMethodId(), context))
                    .append(" → ").append(getMethodName(relation.getTargetMethodId(), context))
                    .append(" (").append(relation.getRelationType()).append(")\n");
        }
        
        prompt.append("\n请生成包含以下内容的说明书：\n");
        prompt.append("1. **业务流程概述** - 简要描述该方法的主要业务功能\n");
        prompt.append("2. **核心方法说明** - 说明主要方法的作用和职责\n");
        prompt.append("3. **主要业务逻辑** - 描述核心的业务处理流程\n");
        prompt.append("4. **关键数据流转** - 说明重要数据的流转过程\n\n");
        
        prompt.append("**要求**:\n");
        prompt.append("- 重点关注业务逻辑，避免过多技术细节\n");
        prompt.append("- 说明书长度控制在800字以内\n");
        prompt.append("- 使用中文输出，结构清晰\n");
        prompt.append("- 使用Markdown格式\n");
        
        return prompt.toString();
    }
    
    /**
     * 构建第2层（详细流程）提示词
     */
    private String buildLevel2Prompt(DocumentationGenerationContext context) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个资深的Java架构师。请基于已有的核心说明书，补充详细的技术实现说明。\n\n");
        
        // 现有说明书内容
        if (context.getPreviousDocumentation() != null) {
            prompt.append("**现有核心说明书**:\n");
            prompt.append("```\n");
            prompt.append(context.getPreviousDocumentation().getContent());
            prompt.append("\n```\n\n");
        }
        
        // 扩展的调用链
        prompt.append("**扩展调用链**:\n");
        var level2Methods = context.getSubgraph().getMethodsAtLevel(2);
        for (var method : level2Methods) {
            prompt.append("- ").append(method.getSimpleIdentifier());
            if (method.getClassName() != null) {
                prompt.append(" [").append(method.getClassName()).append("]");
            }
            prompt.append("\n");
        }
        
        // 详细调用关系
        prompt.append("\n**详细调用关系**:\n");
        var relations = context.getSubgraph().getRelations().stream()
                .filter(r -> isLevel2Relation(r, context))
                .collect(Collectors.toList());
        
        for (var relation : relations) {
            prompt.append("- ").append(getMethodName(relation.getSourceMethodId(), context))
                    .append(" → ").append(getMethodName(relation.getTargetMethodId(), context))
                    .append(" (").append(relation.getRelationType()).append(")");
            if (relation.getLineNumber() != null) {
                prompt.append(" [行号: ").append(relation.getLineNumber()).append("]");
            }
            prompt.append("\n");
        }
        
        prompt.append("\n请在原有说明书基础上补充以下内容：\n");
        prompt.append("1. **详细方法调用关系** - 补充更详细的方法调用说明\n");
        prompt.append("2. **异常处理机制** - 说明异常处理的策略和流程\n");
        prompt.append("3. **数据验证逻辑** - 描述数据校验和处理逻辑\n");
        prompt.append("4. **性能考虑点** - 提及性能相关的设计考虑\n");
        prompt.append("5. **技术实现细节** - 补充重要的技术实现说明\n\n");
        
        prompt.append("**要求**:\n");
        prompt.append("- 在原有说明书基础上扩展，保持结构清晰\n");
        prompt.append("- 重点补充技术实现细节\n");
        prompt.append("- 说明书总长度控制在2500字以内\n");
        prompt.append("- 使用中文输出，使用Markdown格式\n");
        
        return prompt.toString();
    }
    
    /**
     * 构建第3层（完整文档）提示词
     */
    private String buildLevel3Prompt(DocumentationGenerationContext context) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("你是一个资深的Java架构师。请基于完整的代码调用图谱，生成全面的技术说明书。\n\n");
        
        // 完整调用图信息
        prompt.append("**完整调用图谱**:\n");
        prompt.append("- 总节点数: ").append(context.getSubgraph().getTotalNodes()).append("\n");
        prompt.append("- 总边数: ").append(context.getSubgraph().getTotalEdges()).append("\n");
        prompt.append("- 最大层级: ").append(context.getSubgraph().getMaxLevel()).append("\n\n");
        
        // 按层级列出所有方法
        for (int level = 0; level <= context.getSubgraph().getMaxLevel(); level++) {
            var methods = context.getSubgraph().getMethodsAtLevel(level);
            if (!methods.isEmpty()) {
                prompt.append("**第").append(level).append("层方法**:\n");
                for (var method : methods) {
                    prompt.append("- ").append(method.getFullName());
                    if (method.getVisibility() != null) {
                        prompt.append(" [").append(method.getVisibility()).append("]");
                    }
                    if (Boolean.TRUE.equals(method.getIsStatic())) {
                        prompt.append(" [static]");
                    }
                    prompt.append("\n");
                }
                prompt.append("\n");
            }
        }
        
        // 完整调用关系
        prompt.append("**完整调用关系**:\n");
        var relations = context.getSubgraph().getRelations();
        for (var relation : relations) {
            prompt.append("- ").append(getMethodName(relation.getSourceMethodId(), context))
                    .append(" → ").append(getMethodName(relation.getTargetMethodId(), context))
                    .append(" (").append(relation.getRelationType()).append(")");
            if (relation.getDependencyType() != null) {
                prompt.append(" [").append(relation.getDependencyType()).append("]");
            }
            prompt.append("\n");
        }
        
        prompt.append("\n请生成包含以下内容的完整技术说明书：\n");
        prompt.append("1. **完整业务流程图** - 详细的业务流程描述\n");
        prompt.append("2. **架构设计说明** - 整体架构和设计思路\n");
        prompt.append("3. **所有方法详细说明** - 每个重要方法的功能和实现\n");
        prompt.append("4. **数据结构定义** - 涉及的主要数据结构\n");
        prompt.append("5. **异常处理策略** - 完整的异常处理机制\n");
        prompt.append("6. **性能优化建议** - 性能相关的建议和注意事项\n");
        prompt.append("7. **维护注意事项** - 代码维护和扩展的注意点\n");
        prompt.append("8. **依赖关系分析** - 模块间的依赖关系说明\n\n");
        
        prompt.append("**要求**:\n");
        prompt.append("- 结构化输出，层次清晰\n");
        prompt.append("- 技术细节完整，适合开发人员深度参考\n");
        prompt.append("- 包含代码示例和关键逻辑说明\n");
        prompt.append("- 说明书长度控制在8000字以内\n");
        prompt.append("- 使用中文输出，使用Markdown格式\n");
        
        return prompt.toString();
    }
    
    /**
     * 判断是否为第1层关系
     */
    private boolean isLevel1Relation(SubgraphData.CallRelation relation, DocumentationGenerationContext context) {
        var sourceMethod = findMethodById(relation.getSourceMethodId(), context);
        var targetMethod = findMethodById(relation.getTargetMethodId(), context);
        
        return (sourceMethod != null && sourceMethod.getLevel() != null && sourceMethod.getLevel() <= 1) ||
               (targetMethod != null && targetMethod.getLevel() != null && targetMethod.getLevel() <= 1);
    }
    
    /**
     * 判断是否为第2层关系
     */
    private boolean isLevel2Relation(SubgraphData.CallRelation relation, DocumentationGenerationContext context) {
        var sourceMethod = findMethodById(relation.getSourceMethodId(), context);
        var targetMethod = findMethodById(relation.getTargetMethodId(), context);
        
        return (sourceMethod != null && sourceMethod.getLevel() != null && sourceMethod.getLevel() <= 2) ||
               (targetMethod != null && targetMethod.getLevel() != null && targetMethod.getLevel() <= 2);
    }
    
    /**
     * 根据方法ID获取方法名称
     */
    private String getMethodName(String methodId, DocumentationGenerationContext context) {
        var method = findMethodById(methodId, context);
        return method != null ? method.getSimpleIdentifier() : methodId;
    }
    
    /**
     * 根据ID查找方法信息
     */
    private MethodInfo findMethodById(String methodId, DocumentationGenerationContext context) {
        return context.getSubgraph().getAllMethods().stream()
                .filter(m -> methodId.equals(m.getMethodId()))
                .findFirst()
                .orElse(null);
    }
}
