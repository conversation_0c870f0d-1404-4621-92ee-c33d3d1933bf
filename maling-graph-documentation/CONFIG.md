# 配置文件说明

## 配置文件结构

项目采用Spring Boot标准的配置文件分层结构，避免重复配置：

```
src/main/resources/
├── application.properties          # 通用配置（所有环境共享）
├── application-dev.properties      # 开发环境特有配置
└── application-prod.properties     # 生产环境特有配置
```

## 配置分层原则

### 1. application.properties (通用配置)
包含所有环境都相同的配置：
- 应用名称
- MyBatis基础配置
- 服务器基础配置
- 文档生成的通用参数

### 2. application-dev.properties (开发环境)
只包含开发环境特有的配置：
- 开发数据库连接（较小的连接池）
- 自动建表配置
- 详细的日志输出
- 保守的并发设置

### 3. application-prod.properties (生产环境)
只包含生产环境特有的配置：
- 生产数据库连接（较大的连接池）
- 禁用自动建表
- 精简的日志输出
- 优化的性能设置

## 配置优先级

Spring Boot配置加载顺序：
1. `application.properties` (基础配置)
2. `application-{profile}.properties` (环境特定配置)

环境特定配置会覆盖基础配置中的同名属性。

## 主要配置项对比

| 配置项 | 通用配置 | 开发环境 | 生产环境 |
|--------|----------|----------|----------|
| 数据库URL | - | localhost:3306/dev | mysql-server:3306/prod |
| 连接池大小 | - | 2-10 | 10-50 |
| 自动建表 | - | 启用 | 禁用 |
| 日志级别 | - | DEBUG | INFO/WARN |
| 并发任务数 | - | 3 | 10 |
| 归档延迟 | - | 7天 | 3天 |

## 环境变量支持

所有环境都支持通过环境变量覆盖配置：

```bash
# 数据库配置
export DB_URL="*****************************************?..."
export DB_USERNAME="your-username"
export DB_PASSWORD="your-password"

# 服务器配置（仅生产环境）
export SERVER_PORT="8080"
```

## 使用方法

### 开发环境启动
```bash
# 方式1：通过参数指定
./gradlew bootRun --args='--spring.profiles.active=dev'

# 方式2：通过环境变量
export SPRING_PROFILES_ACTIVE=dev
./gradlew bootRun
```

### 生产环境启动
```bash
# 方式1：通过参数指定
java -jar app.jar --spring.profiles.active=prod

# 方式2：通过环境变量
export SPRING_PROFILES_ACTIVE=prod
java -jar app.jar
```

## 配置验证

启动时可以通过以下方式验证配置是否正确加载：

1. **查看启动日志**：
   ```
   The following profiles are active: dev
   ```

2. **访问健康检查端点**：
   ```bash
   curl http://localhost:8080/documentation/actuator/health
   ```

3. **查看配置信息**（开发环境）：
   ```bash
   curl http://localhost:8080/documentation/actuator/configprops
   ```

## 配置最佳实践

1. **敏感信息**：使用环境变量，不要硬编码在配置文件中
2. **默认值**：为环境变量提供合理的默认值
3. **环境隔离**：不同环境使用不同的数据库和配置
4. **性能调优**：生产环境使用优化的连接池和并发设置
5. **日志管理**：生产环境减少日志输出，开发环境详细记录

## 故障排除

### 配置不生效
1. 检查profile是否正确激活
2. 确认配置文件名称正确
3. 验证配置语法（properties格式）

### 数据库连接失败
1. 检查数据库服务是否启动
2. 验证连接字符串和凭据
3. 确认网络连通性

### 性能问题
1. 调整连接池大小
2. 优化并发任务数
3. 检查日志级别设置
