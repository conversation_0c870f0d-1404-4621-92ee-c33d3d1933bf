# Maling Graph Documentation - Spring Boot版本

## 概述

基于Spring Boot框架的渐进式说明书生成系统，集成了MyBatis进行数据库操作，提供完整的REST API接口。

## 技术栈

- **Spring Boot 3.2.0**: 主框架
- **MyBatis 3.0.3**: 数据库ORM框架
- **MySQL**: 数据库（开发和生产环境）
- **Spring Web**: REST API
- **Spring Scheduling**: 定时任务

## 快速启动

### 1. 数据库准备

首先创建MySQL数据库：

```sql
-- 创建数据库
CREATE DATABASE maling_documentation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建开发环境数据库
CREATE DATABASE maling_documentation_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'maling_user'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON maling_documentation.* TO 'maling_user'@'%';
GRANT ALL PRIVILEGES ON maling_documentation_dev.* TO 'maling_user'@'%';
FLUSH PRIVILEGES;
```

### 2. 配置数据库连接

设置环境变量或修改配置文件：

```bash
# 环境变量方式
export DB_URL="******************************************************************************************************************************************************************"
export DB_USERNAME="root"
export DB_PASSWORD="your_password"
```

### 3. 启动应用

```bash
# 进入项目目录
cd maling-graph-documentation

# 开发环境启动
./gradlew bootRun --args='--spring.profiles.active=dev'

# 生产环境启动
./gradlew bootRun --args='--spring.profiles.active=prod'

# 或者构建后运行
./gradlew build
java -jar build/libs/maling-graph-documentation-1.0.jar --spring.profiles.active=dev
```

### 4. 访问应用

- **应用地址**: http://localhost:8080/documentation
- **健康检查**: http://localhost:8080/documentation/actuator/health
- **API接口**: http://localhost:8080/documentation/api

## REST API接口

### 文档生成接口

#### 1. 为所有入口点生成说明书
```http
POST /api/documentation/generate/all?level=3
```

**响应示例**:
```json
{
  "success": true,
  "message": "成功启动 5 个生成任务",
  "data": [1, 2, 3, 4, 5]
}
```

#### 2. 为指定入口点生成说明书
```http
POST /api/documentation/generate/{entryPointId}?level=3
```

**响应示例**:
```json
{
  "success": true,
  "message": "成功启动生成任务",
  "data": 1
}
```

### 任务管理接口

#### 3. 获取任务状态
```http
GET /api/documentation/task/{taskId}
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取任务状态成功",
  "data": {
    "id": 1,
    "entryPointId": "com.example.service.UserService.createUser",
    "status": "RUNNING",
    "currentLevel": 2,
    "targetLevel": 3,
    "progress": 66,
    "createdAt": "2024-01-01T10:00:00",
    "updatedAt": "2024-01-01T10:05:00"
  }
}
```

#### 4. 取消任务
```http
POST /api/documentation/task/{taskId}/cancel
```

#### 5. 获取所有活跃任务
```http
GET /api/documentation/tasks
```

### 文档查询接口

#### 6. 获取入口点的说明书
```http
GET /api/documentation/entry-point/{entryPointId}?level=3
```

#### 7. 获取最终版本说明书
```http
GET /api/documentation/entry-point/{entryPointId}/final
```

### 系统管理接口

#### 8. 获取系统统计信息
```http
GET /api/documentation/statistics
```

#### 9. 清理过期数据
```http
POST /api/documentation/cleanup
```

## 数据库配置

### 开发环境MySQL配置

```properties
# application-dev.properties
spring.datasource.url=${DB_URL:******************************************************************************************************************************************************************}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:password}
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.maximum-pool-size=10
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql
```

### 生产环境MySQL配置

```properties
# application-prod.properties
spring.datasource.url=${DB_URL:***************************************************?useUnicode=true&characterEncoding=utf8&useSSL=true&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.maximum-pool-size=50
# 生产环境不自动执行建表脚本
spring.sql.init.mode=never
```

## MyBatis配置

### 注解式Mapper示例

```java
@Mapper
@Repository
public interface DocumentationMapper {
    
    @Insert("""
        INSERT INTO documentation (
            entry_point_id, entry_point_name, title, content, level, status
        ) VALUES (
            #{entryPointId}, #{entryPointName}, #{title}, #{content}, #{level}, #{status}
        )
    """)
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Documentation documentation);
    
    @Select("SELECT * FROM documentation WHERE id = #{id}")
    Documentation findById(@Param("id") Long id);
    
    @Update("""
        UPDATE documentation SET 
            title = #{title}, content = #{content}, status = #{status}
        WHERE id = #{id}
    """)
    int update(Documentation documentation);
    
    @Delete("DELETE FROM documentation WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
}
```

## 定时任务

系统包含以下定时任务：

1. **清理过期任务**: 每天凌晨2点执行
2. **清理过期归档**: 每周日凌晨3点执行  
3. **系统状态统计**: 每小时执行一次

## 异步处理

系统使用Spring的异步处理机制：

```java
@Configuration
public class DocumentationSpringConfig implements AsyncConfigurer {
    
    @Override
    @Bean(name = "taskExecutor")
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("Documentation-");
        executor.initialize();
        return executor;
    }
}
```

## 使用示例

### Java客户端示例

```java
@RestController
public class ExampleController {
    
    @Autowired
    private DocumentationGenerator documentationGenerator;
    
    @PostMapping("/example/generate")
    public ResponseEntity<?> generateExample() {
        // 为特定入口点生成说明书
        CompletableFuture<Long> future = documentationGenerator
            .generateDocumentationForEntryPoint("com.example.UserService.createUser", 3);
        
        Long taskId = future.join();
        
        // 监控任务进度
        DocumentationTask task = documentationGenerator.getTaskStatus(taskId);
        
        return ResponseEntity.ok(task);
    }
}
```

### curl命令示例

```bash
# 为所有入口点生成说明书
curl -X POST "http://localhost:8080/documentation/api/documentation/generate/all?level=3"

# 获取任务状态
curl -X GET "http://localhost:8080/documentation/api/documentation/task/1"

# 获取说明书
curl -X GET "http://localhost:8080/documentation/api/documentation/entry-point/com.example.UserService.createUser"
```

## 监控和日志

### 日志配置

```yaml
logging:
  level:
    com.webank.maling.documentation: DEBUG
    com.webank.maling.documentation.mapper: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### 系统监控

- 通过 `/api/documentation/statistics` 获取系统统计信息
- 通过 `/api/documentation/tasks` 监控活跃任务
- 查看应用日志了解详细执行情况

## 部署说明

### Docker部署

```dockerfile
FROM openjdk:21-jdk-slim

COPY build/libs/maling-graph-documentation-1.0.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 生产环境配置

```yaml
# application-prod.yml
spring:
  profiles:
    active: prod
  datasource:
    url: ***************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    
server:
  port: 8080
  
logging:
  level:
    com.webank.maling.documentation: INFO
  file:
    name: logs/documentation.log
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务是否启动

2. **任务执行失败**
   - 查看任务错误信息
   - 检查AI服务配置

3. **内存不足**
   - 调整JVM参数：`-Xmx2g -Xms1g`
   - 减少并发任务数量

### 性能优化

1. **数据库优化**
   - 添加适当的索引
   - 调整连接池配置

2. **线程池优化**
   - 根据服务器配置调整线程池大小
   - 监控线程池使用情况

3. **缓存优化**
   - 启用MyBatis二级缓存
   - 添加Redis缓存层
