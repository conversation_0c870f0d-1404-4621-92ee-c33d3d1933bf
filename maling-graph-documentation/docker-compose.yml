version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: maling-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: maling_documentation
      MYSQL_USER: maling_user
      MYSQL_PASSWORD: maling123456
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - maling-network
    restart: unless-stopped

  # 说明书生成应用
  documentation-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: maling-documentation
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_URL: **********************************************************************************************************************************************************
      DB_USERNAME: maling_user
      DB_PASSWORD: maling123456
      SERVER_PORT: 8080
    ports:
      - "8080:8080"
    depends_on:
      - mysql
    networks:
      - maling-network
    restart: unless-stopped
    volumes:
      - app_logs:/app/logs
      - app_data:/data/documentation

volumes:
  mysql_data:
    driver: local
  app_logs:
    driver: local
  app_data:
    driver: local

networks:
  maling-network:
    driver: bridge
