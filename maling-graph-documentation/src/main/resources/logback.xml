<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}/}spring.log}"/>
    <property name="IMS_LOG_PATTERN" value="[%level][%d{yyyy-MM-dd HH:mm:ss SSS}][%t][][%X{SRC_BIZ_SEQ}][][][%m][]%n"/>
    <property name="METRICS_LOG_PATTERN" value="[%level][%d{yyyy-MM-dd HH:mm:ss SSS}][%m]%n"/>
    <property name="COMMON_LOG_PATTERN" value="[%level] %d{yyyy-MM-dd HH:mm:ss.SSS}[%t][%X{SRC_BIZ_SEQ}] %logger{32}:%L : [%m][]%n"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${LOG_FILE}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${LOG_FILE}.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>512MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>102400</queueSize>
        <discardingThreshold>0</discardingThreshold>
        <includeCallerData>true</includeCallerData>
        <appender-ref ref="FILE"/>
    </appender>

    <appender name="SQL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${LOG_FILE}-sql.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${LOG_FILE}-sql.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>512MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <logger name="org.apache.ibatis" level="DEBUG" additivity="false">
        <appender-ref ref="SQL"/>
    </logger>

    <logger name="org.mybatis.spring" level="DEBUG" additivity="false">
        <appender-ref ref="SQL"/>
    </logger>

    <logger name="com.webank.maling.documentation.repository.sql.mapper" level="DEBUG" additivity="false">
        <appender-ref ref="SQL"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="ASYNC"/>
    </root>
</configuration>
