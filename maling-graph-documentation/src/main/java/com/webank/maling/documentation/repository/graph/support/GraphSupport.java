package com.webank.maling.documentation.repository.graph.support;

import com.vesoft.nebula.client.graph.data.Node;
import com.vesoft.nebula.client.graph.data.Relationship;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.webank.maling.base.model.MethodInfo;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.util.Map;

@Slf4j
@UtilityClass
public class GraphSupport {

    /**
     ** 将Vertex解析为MethodInfo
     */
    public MethodInfo parseVertexToMethodInfo(Node node) {
        try {
            String methodId = node.getId().toString();
            var properties = node.properties("function");

            String fullName = getStringProperty(properties, "full_name");
            String methodName = getStringProperty(properties, "name");

            return MethodInfo.builder()
                    .methodId(methodId)
                    .methodName(methodName)
                    .fullName(fullName)
                    .signature(fullName) // 使用fullName作为签名
                    .className(extractClassName(fullName))
                    .packageName(extractPackageName(fullName))
                    .simpleIdentifier(extractSimpleIdentifier(fullName))
                    .visibility(getStringProperty(properties, "visibility"))
                    .isStatic(getBooleanProperty(properties, "is_static"))
                    .isAbstract(getBooleanProperty(properties, "is_abstract"))
                    .isFinal(getBooleanProperty(properties, "is_final"))
                    .isEntryPoint(getBooleanProperty(properties, "is_entry_point"))
                    .returnType(getStringProperty(properties, "return_type"))
                    .parameterTypes(getStringProperty(properties, "parameter_types"))
                    .exceptionTypes(getStringProperty(properties, "exception_types"))
                    .sourceFile(getStringProperty(properties, "source_file"))
                    .startLine(getIntegerProperty(properties, "line_start"))
                    .endLine(getIntegerProperty(properties, "line_end"))
                    .complexityScore(getIntegerProperty(properties, "complexity"))
                    .description(getStringProperty(properties, "description"))
                    .build();

        } catch (Exception e) {
            log.warn("解析Vertex为MethodInfo时发生错误", e);
            return null;
        }
    }

    // 辅助方法
    private String extractClassName(String fullName) {
        if (fullName == null || fullName.isEmpty()) {
            return null;
        }
        int lastDotIndex = fullName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            int secondLastDotIndex = fullName.lastIndexOf('.', lastDotIndex - 1);
            if (secondLastDotIndex > 0) {
                return fullName.substring(secondLastDotIndex + 1, lastDotIndex);
            } else {
                return fullName.substring(0, lastDotIndex);
            }
        }
        return fullName;
    }

    /**
     * 从全限定名中提取包名
     */
    private String extractPackageName(String fullName) {
        if (fullName == null || fullName.isEmpty()) {
            return null;
        }

        // 查找最后两个点之前的部分作为包名
        int lastDotIndex = fullName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            int secondLastDotIndex = fullName.lastIndexOf('.', lastDotIndex - 1);
            if (secondLastDotIndex > 0) {
                return fullName.substring(0, secondLastDotIndex);
            }
        }

        return null;
    }

    /**
     * 从全限定名中提取简单标识符（类名.方法名）
     */
    private String extractSimpleIdentifier(String fullName) {
        if (fullName == null || fullName.isEmpty()) {
            return null;
        }

        // 查找最后两个点之间的部分
        int lastDotIndex = fullName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            int secondLastDotIndex = fullName.lastIndexOf('.', lastDotIndex - 1);
            if (secondLastDotIndex > 0) {
                return fullName.substring(secondLastDotIndex + 1);
            } else {
                return fullName;
            }
        }

        return fullName;
    }

    private String getStringProperty(Map<String, ValueWrapper> properties, String key) throws UnsupportedEncodingException {
        ValueWrapper wrapper = properties.get(key);
        return (wrapper != null && wrapper.isString()) ? wrapper.asString() : null;
    }

    private Boolean getBooleanProperty(Map<String, ValueWrapper> properties, String key) {
        ValueWrapper wrapper = properties.get(key);
        return (wrapper != null && wrapper.isBoolean()) ? wrapper.asBoolean() : null;
    }

    private Integer getIntegerProperty(Map<String, ValueWrapper> properties, String key) {
        ValueWrapper wrapper = properties.get(key);
        return (wrapper != null && wrapper.isLong()) ? (int) wrapper.asLong() : null;
    }

    public String getEdgeStringProperty(Relationship edge, String key) {
        try {
            var properties = edge.properties();
            ValueWrapper wrapper = properties.get(key);
            return (wrapper != null && wrapper.isString()) ? wrapper.asString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    public Integer getEdgeIntProperty(Relationship edge, String key) {
        try {
            var properties = edge.properties();
            ValueWrapper wrapper = properties.get(key);
            return (wrapper != null && wrapper.isLong()) ? (int) wrapper.asLong() : null;
        } catch (Exception e) {
            return null;
        }
    }
}
