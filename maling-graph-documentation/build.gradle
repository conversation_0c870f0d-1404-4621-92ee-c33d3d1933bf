description = 'Maling Graph Documentation - 说明书生成器'

dependencies {
    // 依赖核心模块
    api project(':maling-graph-base')
    api project(':maling-graph-ai')
    api project(':maling-graph-repository')

    // Spring Boot
    implementation 'org.springframework.boot:spring-boot-starter:3.2.0'
    implementation 'org.springframework.boot:spring-boot-starter-web:3.2.0'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc:3.2.0'
    implementation 'org.springframework.boot:spring-boot-starter-validation:3.2.0'

    // MyBatis
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.3'

    // Database
    implementation 'mysql:mysql-connector-java:8.0.33'

    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"

    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"

    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test:3.2.0'
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}
