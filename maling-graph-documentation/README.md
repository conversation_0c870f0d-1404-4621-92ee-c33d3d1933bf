# Maling Graph Documentation - 渐进式说明书生成器

## 概述

这是一个基于Java代码知识图谱的渐进式说明书生成系统。系统能够从NebulaGraph中获取入口节点和子图信息，通过AI大模型生成分层次的技术说明书，并支持中间态数据的自动归档管理。

## 核心特性

### 🚀 渐进式生成策略
- **第1层（核心流程）**：生成1-2步调用链的核心业务流程说明书
- **第2层（详细流程）**：扩展到3-5步调用链的详细技术实现说明书  
- **第3层（完整文档）**：包含10步完整子图的全面技术文档

### 📊 智能数据管理
- **中间态记录**：保存每个生成层级的中间结果
- **自动归档**：最终版本完成后自动归档中间态数据
- **版本控制**：支持多版本管理和历史追溯
- **存储优化**：灵活的存储策略（数据库/文件系统）

### 🤖 AI驱动生成
- **分层提示词**：针对不同层级优化的AI提示策略
- **上下文感知**：基于前一层级内容进行增量生成
- **重试机制**：自动处理AI服务异常和重试
- **内容限制**：根据层级控制生成内容的长度和复杂度

### ⚡ 高性能处理
- **异步生成**：支持并发处理多个入口点
- **任务管理**：完整的任务状态跟踪和进度监控
- **资源控制**：可配置的并发数和超时控制
- **错误恢复**：完善的异常处理和恢复机制

## 系统架构

```
maling-graph-documentation/
├── entity/                 # 数据实体
│   ├── Documentation.java           # 说明书实体
│   ├── DocumentationMethod.java     # 方法信息实体
│   ├── DocumentationTask.java       # 生成任务实体
│   ├── DocumentationArchive.java    # 归档实体
│   └── DocumentationMethodArchive.java
├── dto/                    # 数据传输对象
│   ├── SubgraphData.java           # 子图数据
│   └── MethodInfo.java             # 方法信息
├── service/                # 服务层
│   ├── EntryPointService.java      # 入口点服务
│   ├── SubgraphService.java        # 子图查询服务
│   ├── AIDocumentationService.java # AI文档生成服务
│   ├── DocumentationArchiveService.java # 归档服务
│   ├── DocumentationTaskService.java    # 任务管理服务
│   └── ProgressiveDocumentationService.java # 渐进式生成服务
├── generator/              # 生成器
│   └── DocumentationGenerator.java # 主生成器
├── config/                 # 配置
│   └── DocumentationConfig.java    # 配置管理
└── example/                # 使用示例
    └── DocumentationGeneratorExample.java
```

## 数据流程

### 1. 渐进式生成流程
```
入口点查询 → 第1层子图 → AI生成核心说明书 → 保存中间态
    ↓
第2层子图 → AI增量生成详细说明书 → 保存中间态
    ↓  
第3层子图 → AI生成完整说明书 → 标记最终版本 → 归档中间态
```

### 2. 数据生命周期
```
中间态数据 → 最终版本完成 → 自动归档 → 定期清理
```

## 配置说明

### 核心配置项

```properties
# 渐进式生成配置
documentation.level1.max_steps=2      # 第1层最大步数
documentation.level2.max_steps=5      # 第2层最大步数  
documentation.level3.max_steps=10     # 第3层最大步数

# 文档内容限制
documentation.level1.max_length=1000  # 第1层最大字符数
documentation.level2.max_length=3000  # 第2层最大字符数
documentation.level3.max_length=10000 # 第3层最大字符数

# 归档策略
documentation.archive.auto_enabled=true           # 自动归档
documentation.archive.delay_days=7                # 归档延迟天数
documentation.archive.max_intermediate_versions=5 # 最大中间版本数

# 并发控制
documentation.concurrent.max_tasks=3              # 最大并发任务数
documentation.concurrent.timeout_minutes=30       # 任务超时时间
```

## 使用方法

### 1. 基本使用

```java
// 初始化生成器
try (NebulaGraphClient nebulaClient = new NebulaGraphClient();
     DocumentationGenerator generator = new DocumentationGenerator(nebulaClient)) {
    
    // 为所有入口点生成核心流程说明书
    CompletableFuture<List<Long>> future = 
        generator.generateDocumentationForAllEntryPoints(1);
    
    List<Long> taskIds = future.join();
    System.out.println("已启动 " + taskIds.size() + " 个生成任务");
}
```

### 2. 单个入口点生成

```java
// 为特定入口点生成完整说明书
String entryPointId = "com.example.service.UserService.createUser";
CompletableFuture<Long> future = 
    generator.generateDocumentationForEntryPoint(entryPointId, 3);

Long taskId = future.join();
```

### 3. 监控进度

```java
// 获取任务状态
DocumentationTask task = generator.getTaskStatus(taskId);
System.out.println("任务状态: " + task.getStatus());
System.out.println("当前层级: " + task.getCurrentLevel());
System.out.println("进度: " + task.getProgress() + "%");
```

### 4. 获取生成结果

```java
// 获取说明书
Documentation doc = generator.getDocumentation(entryPointId, null);
if (doc != null) {
    System.out.println("标题: " + doc.getTitle());
    System.out.println("层级: " + doc.getLevel());
    System.out.println("内容: " + doc.getContent());
}
```

## AI提示词策略

### 第1层（核心流程）
- 重点关注业务逻辑概述
- 控制在1000字以内
- 突出主要方法调用关系

### 第2层（详细流程）  
- 基于第1层内容进行扩展
- 添加异常处理和数据验证逻辑
- 控制在3000字以内

### 第3层（完整文档）
- 包含所有技术实现细节
- 提供完整的方法说明和数据结构
- 适合开发人员深度参考

## 数据库设计

### 主要表结构

- **documentation**: 说明书主表
- **documentation_method**: 方法信息表
- **documentation_task**: 任务管理表
- **documentation_archive**: 归档表
- **documentation_method_archive**: 方法归档表

### 关键字段

- `isFinalVersion`: 标识是否为最终版本
- `parentDocumentationId`: 关联父文档（用于版本链）
- `level`: 生成层级（1-3）
- `archiveReason`: 归档原因

## 性能优化

1. **并发控制**: 可配置的线程池大小
2. **内存管理**: 大子图的流式处理
3. **缓存策略**: AI调用结果缓存
4. **批量操作**: 数据库批量插入和更新
5. **异步处理**: 非阻塞的任务执行

## 扩展点

1. **存储适配器**: 支持不同的存储后端
2. **AI服务适配器**: 支持不同的AI模型
3. **提示词模板**: 可自定义的提示词策略
4. **归档策略**: 可插拔的归档规则
5. **通知机制**: 任务完成通知

## 注意事项

1. **资源管理**: 确保正确关闭生成器和相关资源
2. **异常处理**: 处理AI服务不可用的情况
3. **数据一致性**: 确保归档操作的原子性
4. **性能监控**: 监控任务执行时间和资源使用
5. **配置调优**: 根据实际环境调整并发数和超时时间

## 依赖要求

- Java 21+
- NebulaGraph 3.x
- 支持的AI服务（OpenAI等）
- 可选的关系型数据库（用于元数据存储）
