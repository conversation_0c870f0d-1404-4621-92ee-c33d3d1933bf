package com.webank.maling.analyzer.java.strategy.impl;

import com.webank.maling.analyzer.java.strategy.NodeContentStrategy;
import com.webank.maling.base.model.AnnotationNode;
import com.webank.maling.base.model.Node;

/**
 * 注解节点内容获取策略
 *
 * <AUTHOR> Assistant
 */
public class AnnotationNodeStrategy implements NodeContentStrategy {
    
    @Override
    public String getDigest(Node node, Object context) {
        if (!(node instanceof AnnotationNode annotationNode)) {
            throw new IllegalArgumentException("Node must be AnnotationNode");
        }
        
        return "Annotation: " + annotationNode.getName() + "\n" + annotationNode.getOriContent();
    }
}
