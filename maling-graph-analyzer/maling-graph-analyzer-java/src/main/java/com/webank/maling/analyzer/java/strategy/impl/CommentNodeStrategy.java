package com.webank.maling.analyzer.java.strategy.impl;

import com.webank.maling.base.model.CommentNode;
import com.webank.maling.base.model.Node;
import com.webank.maling.analyzer.java.strategy.NodeContentStrategy;

/**
 * 注释节点内容获取策略
 * 通用策略，适用于所有处理器中的CommentNode
 * 
 * <AUTHOR> Assistant
 */
public class CommentNodeStrategy implements NodeContentStrategy {
    
    @Override
    public String getDigest(Node node, Object context) {
        if (!(node instanceof CommentNode commentNode)) {
            throw new IllegalArgumentException("Node must be CommentNode");
        }
        
        return commentNode.getOriContent();
    }
}
