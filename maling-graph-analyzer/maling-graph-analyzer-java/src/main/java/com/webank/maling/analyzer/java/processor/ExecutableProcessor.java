package com.webank.maling.analyzer.java.processor;

import com.webank.maling.analyzer.java.context.GraphContext;
import com.webank.maling.analyzer.java.support.ParseSupport;
import com.webank.maling.base.enums.ParseType;
import com.webank.maling.base.enums.RuleEngineType;
import com.webank.maling.base.model.*;
import com.webank.maling.base.util.IdGenerator;
import com.webank.maling.analyzer.java.rule.SpoonEntryPointRuleEngine;
import com.webank.maling.analyzer.java.rule.SpoonRuleContextBuilder;
import com.webank.maling.rule.context.RuleContext;
import com.webank.maling.rule.engine.RuleEngine;
import com.webank.maling.rule.factory.RuleEngineFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import spoon.reflect.code.*;
import spoon.reflect.declaration.*;
import spoon.reflect.reference.CtExecutableReference;
import spoon.reflect.reference.CtTypeReference;
import spoon.reflect.visitor.filter.TypeFilter;

import java.util.*;

import static java.time.LocalDateTime.now;

/**
 * 可执行元素处理器抽象类，用于处理方法和构造函数的共同逻辑
 *
 * @param <T> 可执行元素类型（CtMethod或CtConstructor）
 */
@Slf4j
public abstract class ExecutableProcessor<T extends CtExecutable<?>> extends BaseProcessor<T> {
    // 已处理的可执行元素集合
    protected final Set<String> processedExecutables = new HashSet<>();
    private final Map<String, String> annotationValueToClass;

    public ExecutableProcessor(GraphContext graphContext, Map<String, String> annotationValueToClass) {
        super(graphContext);
        this.annotationValueToClass = annotationValueToClass;
    }

    /**
     * 处理可执行元素
     *
     * @param element 可执行元素
     */
    public void processExecutable(T element) {
        try {
            if(Objects.nonNull(element.getReference().getDeclaringType()) && Objects.nonNull(element.getReference().getDeclaringType().getTopLevelType().getPackage())) {
                CtTypeReference<?> ctTypeReference = Objects.isNull(element.getReference().getDeclaringType().getDeclaringType()) ?
                        element.getReference().getDeclaringType() : element.getReference().getDeclaringType().getDeclaringType();
                String qualifiedName = ctTypeReference.getPackage().getQualifiedName();
                if (StringUtils.isNotBlank(qualifiedName) && StringUtils.isNotBlank(config.getTargetPackage()) && !qualifiedName.startsWith(config.getTargetPackage())) {
                    return;
                }
            }
            // 跳过隐式元素（编译器自动生成的）
            if (element.isImplicit()) {
                log.debug("Skipping implicit executable: {}", element);
                return;
            }
            if(ParseSupport.isSimpleGetter(element) || ParseSupport.isSimpleSetter(element)) {
                log.debug("Skipping simple getter and setter: {}", element);
                return;
            }

            CtType<?> declaringType = getDeclaringType(element);
            if (declaringType == null) {
                return;
            }

            String executableName = getExecutableName(element, declaringType);
            String fullName = ParseSupport.getExecutableSignature(declaringType.getQualifiedName(), element);
            String executableId = IdGenerator.generate(IdGenerator.builder()
                    .fullQualifiedName(fullName)
                    .isShadow(false).build());

            // 避免重复处理
            if (processedExecutables.contains(executableId)) {
                return;
            }
            processedExecutables.add(executableId);

            // 确定可见性
            String visibility = determineVisibility(element);

            // 创建函数节点
            FunctionNode functionNode = createFunctionNode(element, executableId, executableName, fullName, visibility);
            processNode(functionNode);

            // 处理注释
            processExecutableComment(element, executableId);

            // 处理注解
            processExecutableAnnotations(element, executableId);

            // 处理参数依赖
            processExecutableParameterDependencies(element, executableId);
            
            // 处理接口实现和方法重写
            processOverride(element, executableId);
            
            // 处理调用依赖
            processExecutableCallDependencies(element, executableId);


        } catch (Exception e) {
            log.error("Failed to process executable: {}", getExecutableName(element, getDeclaringType(element)), e);
        }
    }

    /**
     * 处理可执行元素注释
     *
     * @param element      可执行元素
     * @param executableId 可执行元素ID
     */
    protected void processExecutableComment(T element, String executableId) {
        CtComment comment = element.getComments().stream()
                .filter(c -> c.getCommentType() == CtComment.CommentType.JAVADOC)
                .findFirst()
                .orElse(null);

        if (comment != null) {
            CtType<?> declaringType = getDeclaringType(element);
            String executableSignature = ParseSupport.getExecutableSignature(declaringType.getQualifiedName(), element);
            String commentId = IdGenerator.generate(IdGenerator.builder()
                    .fullQualifiedName(executableSignature + "#comment")
                    .isShadow(false).build());

            CommentNode commentNode = CommentNode.builder()
                    .id(commentId)
                    .nodeType(NodeType.COMMENT)
                    .fullName(declaringType.getQualifiedName() + "#" + executableSignature + "#comment")
                    .type("DOC")
                    .lineStart(comment.getPosition().isValidPosition() ? comment.getPosition().getLine() : -1)
                    .lineEnd(comment.getPosition().isValidPosition() ? comment.getPosition().getEndLine() : -1)
                    .branchName(config.getBranch())
                    .commitStatus("COMMITTED")
                    .lastUpdated(now())
                    .repoId(config.getProjectId())
                    .build();
            commentNode.setContent(comment.toString());

            processNode(commentNode);

            // 添加文档关系
            Edge edge = Edge.builder()
                    .srcId(executableId)
                    .dstId(commentId)
                    .type(EdgeType.DOCUMENTED_BY)
                    .build();

            processEdge(edge);
        }
    }

    /**
     * 处理可执行元素注解
     *
     * @param element      可执行元素
     * @param executableId 可执行元素ID
     */
    protected void processExecutableAnnotations(T element, String executableId) {
        CtType<?> declaringType = getDeclaringType(element);
        String executableSignature = ParseSupport.getExecutableSignature(declaringType.getQualifiedName(), element);

        for (CtAnnotation<?> annotation : element.getAnnotations()) {
            CtTypeReference<?> annotationType = annotation.getAnnotationType();
            String annotationTypeName = annotationType.getQualifiedName();
            if (ParseSupport.isSpringComponentAnnotation(annotationTypeName)) {
                continue;
            }

            CtType<?> typeDeclaration = annotationType.getTypeDeclaration();
            boolean isShadow = typeDeclaration == null || typeDeclaration.isShadow();
            String annotationTypeId = IdGenerator.generate(IdGenerator.builder()
                    .fullQualifiedName(annotationTypeName)
                    .isShadow(isShadow).build());

            String fullName = executableSignature + "#" + annotationType.getSimpleName();
            // 创建注解实例节点
            String annotationId = IdGenerator.generate(IdGenerator.builder()
                    .fullQualifiedName(fullName)
                    .isShadow(false).build());

            boolean isMarker = annotation.getValues().isEmpty();
            AnnotationNode annotationNode = AnnotationNode.builder()
                    .id(annotationId)
                    .nodeType(isMarker ? NodeType.MARKER_ANNOTATION : NodeType.ANNOTATION)
                    .fullName(fullName)
                    .name(annotationType.getSimpleName())
                    .type(isMarker ? NodeType.MARKER_ANNOTATION.name() : NodeType.ANNOTATION.name())
                    .lineStart(annotation.getPosition().isValidPosition() ? annotation.getPosition().getLine() : -1)
                    .lineEnd(annotation.getPosition().isValidPosition() ? annotation.getPosition().getEndLine() : -1)
                    .branchName(config.getBranch())
                    .commitStatus("COMMITTED")
                    .lastUpdated(now())
                    .repoId(config.getProjectId())
                    .build();
            annotationNode.setContent(annotation.toString());
            processNode(annotationNode);

            // 添加可执行元素包含注解关系
            Edge containsEdge = Edge.builder()
                    .srcId(executableId)
                    .dstId(annotationId)
                    .type(EdgeType.CONTAINS)
                    .build();

            processEdge(containsEdge);

            if(!ParseSupport.isIgnoreType(annotationType)) {
                // 添加注解实例化关系
                Edge instanceOfEdge = Edge.builder()
                        .srcId(annotationId)
                        .dstId(annotationTypeId)
                        .type(EdgeType.INSTANCE_OF)
                        .lineNumber(annotation.getPosition().isValidPosition() ? annotation.getPosition().getLine() : -1)
                        .build();

                processEdge(instanceOfEdge);
            }
        }
    }

    /**
     * 处理可执行元素参数依赖
     *
     * @param element      可执行元素
     * @param executableId 可执行元素ID
     */
    protected void processExecutableParameterDependencies(T element, String executableId) {
        for (CtParameter<?> parameter : element.getParameters()) {
            CtTypeReference<?> parameterType = parameter.getType();
            if (ParseSupport.isIgnoreType(parameterType)) {
                continue;
            }
            CtType<?> typeDeclaration = parameterType.getTypeDeclaration();
            boolean isShadow = typeDeclaration == null || typeDeclaration.isShadow();
            String parameterTypeName = parameterType.getQualifiedName();
            String parameterTypeId = IdGenerator.generate(IdGenerator.IdGeneratorContext.builder()
                    .fullQualifiedName(parameterTypeName)
                    .isShadow(isShadow).build());

            Edge edge = Edge.builder()
                    .srcId(executableId)
                    .dstId(parameterTypeId)
                    .type(EdgeType.DEPENDS_ON)
                    .dependencyType(DependencyType.USAGE)
                    .lineNumber(parameter.getPosition().isValidPosition() ? parameter.getPosition().getLine() : -1)
                    .build();

            processEdge(edge);
        }
    }

    /**
     * 处理可执行元素调用依赖
     *
     * @param element      可执行元素
     * @param executableId 可执行元素ID
     */
    protected void processExecutableCallDependencies(T element, String executableId) {
        // 查找方法调用
        element.getElements(new TypeFilter<>(CtInvocation.class)).forEach(invocation -> {
            CtExecutableReference<?> executableRef = invocation.getExecutable();
            if (executableRef == null || executableRef.getDeclaringType() == null) {
                return;
            }

            CtTypeReference<?> declaringType = executableRef.getDeclaringType();
            if(ParseSupport.isIgnoreType(declaringType)){
                return;
            }

            CtType<?> typeDeclaration = declaringType.getTypeDeclaration();

            //外部参数处理
            CtExecutable<?> refExecutable = executableRef.getExecutableDeclaration();
            if(Objects.isNull(refExecutable)){
                log.debug("invocation:{} 方法无法被解析",invocation);
                return;
            }

            if(ParseSupport.isSimpleGetter(refExecutable) || ParseSupport.isSimpleSetter(refExecutable)) {
                return;
            }

            String targetClassName = declaringType.getQualifiedName();

            String targetMethodFullName = ParseSupport.getExecutableSignature(targetClassName, refExecutable);

            boolean isShadow = typeDeclaration == null || typeDeclaration.isShadow();
            String targetMethodId = IdGenerator.generate(IdGenerator.builder()
                    .fullQualifiedName(targetMethodFullName).isShadow(isShadow).build());

            // 获取调用者表达式
            CtExpression<?> target = invocation.getTarget();
            String actualTargetClass = targetClassName;
            boolean isInjection = false;

            // 处理调用者类型
            if (target != null) {
                // 获取字段名
                String fieldName = getFieldNameFromExpression(target);
                if (fieldName != null) {
                    // 获取字段的声明类
                    CtType<?> declareType = getDeclaringType(element);
                    // 查找字段的注解信息
                    String implementationType = findImplementationType(declareType, fieldName, targetClassName);
                    if (implementationType != null) {
                        actualTargetClass = implementationType;
                        isInjection = true;
                        
                        if (log.isDebugEnabled()) {
                            log.debug("依赖注入调用 - 接口: {}, 实现类: {}, 方法: {}", 
                                    targetClassName, implementationType, executableRef.getSignature());
                        }
                    }
                } else if (target instanceof CtThisAccess) {
                    // 处理this调用
                    actualTargetClass = getDeclaringType(element).getQualifiedName();
                } else if (target instanceof CtSuperAccess) {
                    // 处理super调用
                    CtTypeReference<?> superclass = getDeclaringType(element).getSuperclass();
                    if (superclass != null) {
                        actualTargetClass = superclass.getQualifiedName();
                    }
                }
            }

            if (log.isDebugEnabled()) {
                log.debug("方法调用 - 调用者: {}, 目标类: {}, 目标方法: {}, 方法ID: {}", 
                        executableId, actualTargetClass, targetMethodFullName, targetMethodId);
            }

            // 优化：如果是依赖注入调用，优先处理，不进行外部类检测
            if (isInjection) {
                // 对于依赖注入调用，我们需要确保目标方法存在
                // 如果实现类中的方法没有被处理过，我们可能需要使用接口方法作为备选
                String finalTargetMethodId = targetMethodId;
                
                // 检查实现类中是否存在该方法
                CtType<?> implementationType = getType(actualTargetClass);
                if (implementationType != null) {
                    // 尝试在实现类中查找对应的方法
                    CtMethod<?> implementationMethod = null;
                    CtExecutable<?> refDeclaration = executableRef.getDeclaration();
 
                    if (refDeclaration instanceof CtMethod<?> interfaceMethod) {
                        // 遍历实现类中的所有方法
                        for (CtMethod<?> methodInImplementation : implementationType.getAllMethods()) {
                            if (methodInImplementation.isOverriding(interfaceMethod)) {
                                implementationMethod = methodInImplementation;
                                if (log.isDebugEnabled()) {
                                    log.debug("找到覆盖方法: {} in {}", implementationMethod.getSignature(), implementationType.getQualifiedName());
                                }
                                break;
                            }
                        }
                    } else if (log.isDebugEnabled()) {
                         log.debug("引用的可执行文件不是方法: {}", refDeclaration != null ? refDeclaration.getClass().getSimpleName() : "null");
                    }
                    
                    if (implementationMethod != null) {
                        boolean shadow = implementationType.isShadow();
                        // 使用实现类中的实际方法
                        String actualMethodSignature = ParseSupport.getExecutableSignature(actualTargetClass, implementationMethod);
                        finalTargetMethodId = IdGenerator.generate(IdGenerator.builder()
                                .fullQualifiedName(actualMethodSignature).isShadow(shadow).build());
                        
                        if (log.isDebugEnabled()) {
                            log.debug("找到实现类方法 - 实现类: {}, 方法: {}, 方法ID: {}", 
                                    actualTargetClass, actualMethodSignature, finalTargetMethodId);
                        }
                    } else {
                        if (log.isDebugEnabled()) {
                            log.debug("实现类中未找到方法，使用接口方法 - 实现类: {}, 方法签名: {}", 
                                    actualTargetClass, executableRef.getSignature());
                        }
                    }
                }
                
                Edge edge = Edge.builder()
                        .srcId(executableId)
                        .dstId(finalTargetMethodId)
                        .type(EdgeType.INJECTION_CALLS)
                        .lineNumber(invocation.getPosition().isValidPosition() ? invocation.getPosition().getLine() : -1)
                        .build();
                processEdge(edge);
                return;
            }

            // 处理外部类调用 - 只有在非依赖注入的情况下才进行外部类检测
            if(isShadow){
                Edge edge = Edge.builder()
                        .srcId(executableId)
                        .dstId(targetMethodId)
                        .type(EdgeType.OUT_CALLS)
                        .lineNumber(invocation.getPosition().isValidPosition() ? invocation.getPosition().getLine() : -1)
                        .build();

                processEdge(edge);
                return;
            }

            // 确定调用类型
            EdgeType edgeType = determineCallType(element, executableRef, actualTargetClass, isInjection);

            Edge edge = Edge.builder()
                    .srcId(executableId)
                    .dstId(targetMethodId)
                    .type(edgeType)
                    .lineNumber(invocation.getPosition().isValidPosition() ? invocation.getPosition().getLine() : -1)
                    .build();

            processEdge(edge);
        });
    }

    /**
     * 确定调用类型
     */
    private EdgeType determineCallType(T caller, CtExecutableReference<?> callee, String actualTargetClass, boolean isInjection) {
        CtType<?> callerType = getDeclaringType(caller);
        String callerClassName = callerType.getQualifiedName();

        // 如果是依赖注入调用
        if (isInjection) {
            return EdgeType.INJECTION_CALLS;
        }

        // 如果是同一个类中的调用
        if (callerClassName.equals(actualTargetClass)) {
            return EdgeType.CALLS;
        }

        // 检查是否是父类调用
        CtTypeReference<?> superclass = callerType.getSuperclass();
        if (superclass != null && superclass.getQualifiedName().equals(actualTargetClass)) {
            return EdgeType.SUPER_CALLS;
        }

        // 检查是否是接口调用
        for (CtTypeReference<?> interfaceType : callerType.getSuperInterfaces()) {
            if (interfaceType.getQualifiedName().equals(actualTargetClass)) {
                // 检查是否是通过依赖注入的方式调用接口
                // 检查是否是通过直接实现的方式调用接口
                if (callerType.isSubtypeOf(callee.getDeclaringType())) {
                    return EdgeType.INTERFACE_CALLS;
                }
            }
        }

        // 检查是否是子类调用
        if (callerType.isSubtypeOf(callee.getDeclaringType())) {
            return EdgeType.SUBTYPE_CALLS;
        }

        // 默认返回普通调用
        return EdgeType.CALLS;
    }

    /**
     * 从表达式中获取字段名
     */
    private String getFieldNameFromExpression(CtExpression<?> expression) {
        if (expression instanceof CtFieldAccess) {
            return ((CtFieldAccess<?>) expression).getVariable().getSimpleName();
        } else if (expression instanceof CtVariableAccess) {
            return ((CtVariableAccess<?>) expression).getVariable().getSimpleName();
        }
        return null;
    }


    /**
     * 调试方法：输出依赖注入解析信息
     */
    private void debugInjectionResolution(String fieldName, String interfaceType, String qualifierValue, String resolvedImplementation) {
        if (log.isDebugEnabled()) {
            log.debug("依赖注入解析 - 字段: {}, 接口: {}, Qualifier值: {}, 解析结果: {}", 
                    fieldName, interfaceType, qualifierValue, resolvedImplementation);
            
            // 输出annotationValueToClass中的相关映射
            log.debug("当前annotationValueToClass映射:");
            for (Map.Entry<String, String> entry : annotationValueToClass.entrySet()) {
                if (entry.getKey().contains(interfaceType) || 
                    (qualifierValue != null && entry.getKey().contains(qualifierValue))) {
                    log.debug("  {} -> {}", entry.getKey(), entry.getValue());
                }
            }
        }
    }

    /**
     * 查找字段对应的实现类
     */
    private String findImplementationType(CtType<?> declaringType, String fieldName, String interfaceType) {
        // 查找字段
        CtField<?> field = declaringType.getField(fieldName);
        
        if (log.isDebugEnabled()) {
            log.debug("查找字段实现类 - 声明类: {}, 字段名: {}, 接口类型: {}, 字段是否找到: {}", 
                    declaringType.getQualifiedName(), fieldName, interfaceType, field != null);
        }
        
        if (field != null) {
            // 检查字段的注解
            String qualifierValue = null;
            boolean hasAutowired = false;
            boolean hasResource = false;
            
            if (log.isDebugEnabled()) {
                log.debug("字段 {} 的注解数量: {}", fieldName, field.getAnnotations().size());
            }
            
            for (CtAnnotation<?> annotation : field.getAnnotations()) {
                String annotationName = annotation.getAnnotationType().getQualifiedName();
                
                if (log.isDebugEnabled()) {
                    log.debug("检查注解: {}", annotationName);
                }

                if (annotationName.contains("Autowired")) {
                    hasAutowired = true;
                    log.debug("发现@Autowired注解");
                } else if (annotationName.contains("Qualifier")) {
                    // 获取@Qualifier的值
                    qualifierValue = extractQualifierValue(annotation, declaringType);
                    log.debug("发现@Qualifier注解，值: {}", qualifierValue);
                } else if (annotationName.contains("Resource")) {
                    hasResource = true;
                    // 获取@Resource的name值
                    Object value = annotation.getValues().get("name");
                    if (value instanceof CtFieldAccess) {
                        qualifierValue = extractFieldAccessValue((CtFieldAccess<?>) value);
                    } else if (value instanceof CtVariableAccess) {
                        qualifierValue = extractVariableAccessValue((CtVariableAccess<?>) value, declaringType);
                    }
                    log.debug("发现@Resource注解，name值: {}", qualifierValue);
                }
            }

            // 处理@Autowired注解
            if (hasAutowired) {
                String result = resolveAutowiredImplementation(qualifierValue, interfaceType, fieldName);
                debugInjectionResolution(fieldName, interfaceType, qualifierValue, result);
                return result;
            }

            // 处理@Resource注解
            if (hasResource) {
                String result = resolveResourceImplementation(qualifierValue, interfaceType, fieldName, field);
                debugInjectionResolution(fieldName, interfaceType, qualifierValue, result);
                return result;
            }
            
            if (log.isDebugEnabled()) {
                log.debug("字段 {} 没有找到@Autowired或@Resource注解", fieldName);
            }
        } else {
            log.warn("在类 {} 中未找到字段: {}", declaringType.getQualifiedName(), fieldName);
        }
        return null;
    }

    /**
     * 提取@Qualifier注解的值
     */
    private String extractQualifierValue(CtAnnotation<?> annotation, CtType<?> declaringType) {
        Object value = annotation.getValues().get("value");

        String simpleName = value != null ? value.getClass().getSimpleName() : "null";
        if (log.isDebugEnabled()) {
            log.debug("提取@Qualifier值 - 原始值类型: {}, 值: {}",
                    simpleName, value);
        }

        switch (value) {
            case CtFieldAccess<?> ctFieldAccess -> {
                String result = extractFieldAccessValue(ctFieldAccess);
                log.debug("@Qualifier值为字段访问表达式，解析结果: {}", result);
                return result;
            }
            case CtVariableAccess<?> ctVariableAccess -> {
                String result = extractVariableAccessValue(ctVariableAccess, declaringType);
                log.debug("@Qualifier值为变量访问表达式，解析结果: {}", result);
                return result;
            }
            case CtLiteral<?> ctLiteral -> {
                String result = ctLiteral.getValue().toString();
                log.debug("@Qualifier值为字面量，解析结果: {}", result);
                return result;
            }
            case null,default -> {
                log.warn("@Qualifier值类型不支持: {}", simpleName);
                return value != null ? value.toString() : null;
            }
        }
    }

    /**
     * 提取字段访问表达式的值
     */
    private String extractFieldAccessValue(CtFieldAccess<?> fieldAccess) {
        try {
            // 获取字段的声明类型
            String declaringTypeName = fieldAccess.getVariable().getDeclaringType().getQualifiedName();
            String fieldName = fieldAccess.getVariable().getSimpleName();
            
            if (log.isDebugEnabled()) {
                log.debug("提取字段访问表达式 - 声明类型: {}, 字段名: {}", declaringTypeName, fieldName);
            }
            
            // 尝试获取字段的实际值
            CtType<?> declaringType = getType(declaringTypeName);
            if (declaringType != null) {
                CtField<?> field = declaringType.getField(fieldName);
                if (field != null && field.getDefaultExpression() != null) {
                    Object defaultValue = field.getDefaultExpression();
                    if (log.isDebugEnabled()) {
                        log.debug("找到字段默认值 - 类型: {}, 值: {}", 
                                defaultValue.getClass().getSimpleName(), defaultValue);
                    }

                    if (defaultValue instanceof CtLiteral) {
                        String result = ((CtLiteral<?>) defaultValue).getValue().toString();
                        log.debug("字段值为字面量: {}", result);
                        return result;
                    }
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("字段 {} 没有默认值或默认值为null", fieldName);
                    }
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("无法获取类型: {}", declaringTypeName);
                }
            }
            
            // 如果无法获取实际值，返回字段名作为备选
            log.debug("使用字段名作为备选值: {}", fieldName);
            return fieldName;
        } catch (Exception e) {
            log.warn("提取字段访问表达式值时出错", e);
            return fieldAccess.getVariable().getSimpleName();
        }
    }

    /**
     * 提取变量访问表达式的值
     */
    private String extractVariableAccessValue(CtVariableAccess<?> variableAccess, CtType<?> declaringType) {
        try {
            String variableName = variableAccess.getVariable().getSimpleName();
            // 尝试在类中查找变量的值
            CtField<?> variableField = declaringType.getField(variableName);
            if (variableField != null && variableField.getDefaultExpression() != null) {
                Object variableValue = variableField.getDefaultExpression();
                if (variableValue instanceof CtLiteral) {
                    return ((CtLiteral<?>) variableValue).getValue().toString();
                }
            }
            return variableName;
        } catch (Exception e) {
            log.warn("提取变量访问表达式值时出错", e);
            return variableAccess.getVariable().getSimpleName();
        }
    }

    /**
     * 解析@Autowired注解的实现类
     */
    private String resolveAutowiredImplementation(String qualifierValue, String interfaceType, String fieldName) {
        // 1. 首先检查是否有@Qualifier注解
        if (qualifierValue != null) {
            // 1.1 直接查找qualifierValue
            String implementationType = annotationValueToClass.get(qualifierValue);
            if (implementationType != null) {
                return implementationType;
            }

            // 1.2 使用interfaceType#qualifierValue查找
            String interfaceKey = interfaceType + "#" + qualifierValue;
            implementationType = annotationValueToClass.get(interfaceKey);
            if (implementationType != null) {
                return implementationType;
            }
        }

        // 2. 检查是否有@Primary注解的实现类
        String primaryImplementation = findPrimaryImplementation(interfaceType);
        if (primaryImplementation != null) {
            return primaryImplementation;
        }

        // 3. 检查字段名是否匹配某个实现类
        String nameMatchImplementation = findNameMatchImplementation(interfaceType, fieldName);
        if (nameMatchImplementation != null) {
            return nameMatchImplementation;
        }

        // 4. 如果只有一个实现类，直接使用
        String singleImplementation = findSingleImplementation(interfaceType);
        if (singleImplementation != null) {
            return singleImplementation;
        }

        // 5. 如果仍然无法确定，记录警告并返回null
        log.warn("无法确定字段 {} 的具体实现类，存在多个实现类且无法通过规则确定", fieldName);
        return null;
    }

    /**
     * 解析@Resource注解的实现类
     */
    private String resolveResourceImplementation(String qualifierValue, String interfaceType, String fieldName, CtField<?> field) {
        // 1. 检查是否有name值
        if (qualifierValue != null) {
            // 1.1 直接查找qualifierValue
            String implementationType = annotationValueToClass.get(qualifierValue);
            if (implementationType != null) {
                return implementationType;
            }

            // 1.2 使用interfaceType#qualifierValue查找
            String interfaceKey = interfaceType + "#" + qualifierValue;
            implementationType = annotationValueToClass.get(interfaceKey);
            if (implementationType != null) {
                return implementationType;
            }
        }

        // 2. 检查字段类型是否为接口
        CtTypeReference<?> fieldType = field.getType();
        if (fieldType != null && !fieldType.isPrimitive()) {
            String fieldTypeName = fieldType.getQualifiedName();
            // 2.1 如果是接口，使用字段名查找
            if (fieldType.getTypeDeclaration() != null && fieldType.getTypeDeclaration().isInterface()) {
                String nameMatchImplementation = findNameMatchImplementation(fieldTypeName, fieldName);
                if (nameMatchImplementation != null) {
                    return nameMatchImplementation;
                }
            } else {
                // 2.2 如果是具体类，直接返回
                return fieldTypeName;
            }
        }

        // 3. 检查是否有@Primary注解的实现类
        String primaryImplementation = findPrimaryImplementation(interfaceType);
        if (primaryImplementation != null) {
            return primaryImplementation;
        }

        // 4. 如果只有一个实现类，直接使用
        return findSingleImplementation(interfaceType);
    }

    /**
     * 查找带有@Primary注解的实现类
     */
    private String findPrimaryImplementation(String interfaceType) {
        // 遍历所有实现类，查找带有@Primary注解的类
        for (Map.Entry<String, String> entry : annotationValueToClass.entrySet()) {
            if (entry.getKey().startsWith(interfaceType + "#")) {
                String implementationType = entry.getValue();
                // 检查实现类是否有@Primary注解
                if (hasPrimaryAnnotation(implementationType)) {
                    return implementationType;
                }
            }
        }
        return null;
    }

    /**
     * 检查类是否有@Primary注解
     */
    private boolean hasPrimaryAnnotation(String typeName) {
        try {
            CtType<?> type = getType(typeName);
            if (type != null) {
                for (CtAnnotation<?> annotation : type.getAnnotations()) {
                    if (annotation.getAnnotationType().getQualifiedName()
                            .equals("org.springframework.context.annotation.Primary")) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("检查@Primary注解时出错: {}", typeName, e);
        }
        return false;
    }

    /**
     * 根据字段名查找匹配的实现类
     */
    private String findNameMatchImplementation(String interfaceType, String fieldName) {
        // 1. 首先尝试直接使用字段名查找
        String implementationKey = interfaceType + "#" + fieldName;
        String implementationType = annotationValueToClass.get(implementationKey);
        if (implementationType != null) {
            return implementationType;
        }

        // 2. 如果直接查找失败，使用类型名生成默认名称
        String typeName = interfaceType.substring(interfaceType.lastIndexOf('.') + 1);
        // 转换为小写首字母
        String defaultName = typeName.substring(0, 1).toLowerCase() + typeName.substring(1);

        // 使用默认名称查找
        implementationKey = interfaceType + "#" + defaultName;
        return annotationValueToClass.get(implementationKey);
    }

    /**
     * 查找唯一的实现类
     */
    private String findSingleImplementation(String interfaceType) {
        // 遍历所有以接口类型开头的键，返回第一个找到的实现类
        for (Map.Entry<String, String> entry : annotationValueToClass.entrySet()) {
            if (entry.getKey().startsWith(interfaceType + "#")) {
                return entry.getValue();
            }
        }
        return null;
    }

    /**
     * 获取类型
     */
    private CtType<?> getType(String typeName) {
        try {
            return getFactory().Type().get(typeName);
        } catch (Exception e) {
            log.warn("获取类型时出错: {}", typeName, e);
            return null;
        }
    }

    /**
     * 创建函数节点
     *
     * @param element        可执行元素
     * @param executableId   可执行元素ID
     * @param executableName 可执行元素名称
     * @param fullName       完整名称
     * @param visibility     可见性
     * @return 函数节点
     */
    protected FunctionNode createFunctionNode(T element, String executableId, String executableName, String fullName, String visibility) {
        // 判断是否为入口点
        boolean isEntryPoint = determineEntryPoint(element);

        FunctionNode functionNode = FunctionNode.builder()
                .id(executableId)
                .nodeType(NodeType.FUNCTION)
                .fullName(fullName)
                .name(executableName)
                .visibility(visibility)
                .isStatic(isStatic(element))
                .isConstructor(isConstructor(element))
                .isLibrary(ParseType.LIBRARY.equals(parseType))
                .isEntryPoint(isEntryPoint)
                .lineStart(element.getPosition().isValidPosition() ? element.getPosition().getLine() : -1)
                .lineEnd(element.getPosition().isValidPosition() ? element.getPosition().getEndLine() : -1)
                .complexity(calculateComplexity(element))
                .branchName(config.getBranch())
                .commitStatus("COMMITTED")
                .lastUpdated(now())
                .repoId(config.getProjectId())
                .build();
        functionNode.setContent(element.toString());
        return functionNode;
    }

    /**
     * 判断方法是否为入口点
     *
     * @param element 可执行元素
     * @return 是否为入口点
     */
    protected boolean determineEntryPoint(T element) {
        try {
            if(!(element instanceof CtMethod<?> ctMethod)){
                return false;
            }
            // 获取声明类型
            CtType<?> declaringType = getDeclaringType(element);
            if (declaringType == null) {
                return false;
            }

            // 构建规则上下文
            RuleContext ruleContext = SpoonRuleContextBuilder.buildFromSpoon(ctMethod, declaringType);

            // 获取入口点规则引擎
            RuleEngine<CtExecutable<?>, Boolean> entryPointEngine = RuleEngineFactory.getInstance().getEngine(RuleEngineType.JAVA_ENTRY_POINT);
            if (entryPointEngine == null) {
                log.warn("Entry point rule engine not found");
                return false;
            }

            // 执行规则判断
            Boolean result = entryPointEngine.execute(element, ruleContext);

            if (Boolean.TRUE.equals(result)) {
                log.debug("Method {} identified as entry point", element.getSignature());
            }

            return Boolean.TRUE.equals(result);
        } catch (Exception e) {
            log.warn("Failed to determine entry point for method: {}", element.getSignature(), e);
            return false;
        }
    }

    /**
     * 确定可见性
     *
     * @param element 可执行元素
     * @return 可见性
     */
    protected String determineVisibility(T element) {
        // 由于CtExecutable接口本身没有isPublic等方法，需要在子类中实现
        return "default";
    }

    /**
     * 获取可执行元素的声明类型
     *
     * @param element 可执行元素
     * @return 声明类型
     */
    protected abstract CtType<?> getDeclaringType(T element);

    /**
     * 获取可执行元素名称
     *
     * @param element       可执行元素
     * @param declaringType 声明类型
     * @return 可执行元素名称
     */
    protected abstract String getExecutableName(T element, CtType<?> declaringType);

    /**
     * 判断是否为构造函数
     *
     * @param element 可执行元素
     * @return 是否为构造函数
     */
    protected abstract boolean isConstructor(T element);

    /**
     * 判断是否为静态方法
     *
     * @param element 可执行元素
     * @return 是否为静态方法
     */
    protected abstract boolean isStatic(T element);

    /**
     * 计算可执行元素的圈复杂度（Cyclomatic Complexity）
     * 圈复杂度是一种衡量代码复杂度的指标，等于代码中的判断条件数量 + 1
     *
     * @param element 可执行元素
     * @return 圈复杂度
     */
    protected int calculateComplexity(T element) {
        // 基础复杂度为1
        int complexity = 1;

        try {
            // 如果元素没有方法体，返回基础复杂度
            if (element.getBody() == null) {
                return complexity;
            }

            // 计算if语句的复杂度
            complexity += element.getElements(new TypeFilter<>(CtIf.class)).size();

            // 计算case语句的复杂度
            complexity += element.getElements(new TypeFilter<>(CtCase.class)).size();

            // 计算catch语句的复杂度
            complexity += element.getElements(new TypeFilter<>(CtCatch.class)).size();

            // 计算for循环的复杂度
            complexity += element.getElements(new TypeFilter<>(CtFor.class)).size();

            // 计算forEach循环的复杂度
            complexity += element.getElements(new TypeFilter<>(CtForEach.class)).size();

            // 计算while循环的复杂度
            complexity += element.getElements(new TypeFilter<>(CtWhile.class)).size();

            // 计算do-while循环的复杂度
            complexity += element.getElements(new TypeFilter<>(CtDo.class)).size();

            log.debug("方法 {} 的圈复杂度为: {}", element.getSimpleName(), complexity);
        } catch (Exception e) {
            log.error("计算方法 {} 的复杂度时出错: {}", element.getSimpleName(), e.getMessage());
        }

        return complexity;
    }

    /**
     * 处理方法重写/实现关系
     *
     * @param element 当前方法元素
     * @param executableId 当前方法在图谱中的ID
     */
    public void processOverride(T element, String executableId){
        if(element instanceof CtMethod<?> currentMethod) {

            // getTopDefinitions()返回方法定义链:
            // - 对于实现接口方法: 返回接口方法定义
            // - 对于重写父类方法: 返回父类方法定义
            // - 对于无重写的方法: 返回方法自身
            Collection<CtMethod<?>> topDefinitions = currentMethod.getTopDefinitions();
            for (CtMethod<?> topDefinition : topDefinitions) {
                // 跳过自身定义避免创建自引用边
                if(topDefinition.equals(currentMethod)) {
                    continue;
                }

                // 验证方法签名是否匹配(名称+参数类型)
                if(!currentMethod.getSignature().equals(topDefinition.getSignature())) {
                    continue;
                }

                // 获取顶层方法信息
                CtType<?> declaringType = topDefinition.getDeclaringType();
                String topMethodSignature = ParseSupport.getExecutableSignature(
                        declaringType.getQualifiedName(), topDefinition);
                boolean isShadow = declaringType.isShadow();
                String topMethodId = IdGenerator.generate(IdGenerator.builder()
                        .fullQualifiedName(topMethodSignature).isShadow(isShadow).build());

                // 确定关系类型
                EdgeType edgeType;
                if(declaringType.isInterface()) {
                    edgeType = EdgeType.IMPLEMENTED_BY; // 接口实现关系
                } else {
                    // 严格验证重写关系
                    if(!isValidOverride(currentMethod, topDefinition)) {
                        continue;
                    }
                    edgeType = EdgeType.OVERRIDE; // 方法重写关系
                }

                Edge edge = Edge.builder()
                        .srcId(topMethodId)
                        .dstId(executableId)
                        .type(edgeType)
                        .build();

                processEdge(edge);
            }
        }
    }

    /**
     * 验证方法重写是否合法
     */
    private boolean isValidOverride(CtMethod<?> current, CtMethod<?> parent) {
        // 1. 检查父类方法是否可重写(非final)
        if(parent.isFinal()) {
            return false;
        }
        // 2. 检查返回类型是否兼容
        if(!current.getType().isSubtypeOf(parent.getType())) {
            return false;
        }
        // 3. 检查可见性是否不降低
        return !current.isPrivate() || parent.isPrivate();
    }

    @Override
    protected Logger getLogger(){
        return log;
    }
}
