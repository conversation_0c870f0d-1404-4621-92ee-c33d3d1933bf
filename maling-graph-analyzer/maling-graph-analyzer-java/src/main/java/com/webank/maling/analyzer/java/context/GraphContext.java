package com.webank.maling.analyzer.java.context;

import com.webank.maling.ai.vector.OpenAIVectorGenerator;
import com.webank.maling.base.enums.ParseType;
import com.webank.maling.repository.milvus.MilvusClient;
import com.webank.maling.repository.nebula.NebulaGraphClient;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class GraphContext {
    private NebulaGraphClient nebulaGraphClient;
    private MilvusClient milvusClient;
    private OpenAIVectorGenerator openAIVectorGenerator;
    private ParseType parseType;
}
