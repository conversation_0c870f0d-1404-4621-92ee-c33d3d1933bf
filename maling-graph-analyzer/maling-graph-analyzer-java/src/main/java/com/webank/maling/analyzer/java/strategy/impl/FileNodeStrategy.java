package com.webank.maling.analyzer.java.strategy.impl;

import com.webank.maling.base.model.FileNode;
import com.webank.maling.base.model.Node;
import com.webank.maling.analyzer.java.strategy.NodeContentStrategy;

/**
 * 文件节点内容获取策略
 * 
 * <AUTHOR> Assistant
 */
public class FileNodeStrategy implements NodeContentStrategy {
    
    @Override
    public String getDigest(Node node, Object context) {
        if (!(node instanceof FileNode fileNode)) {
            throw new IllegalArgumentException("Node must be FileNode");
        }
        
        return "File: " + fileNode.getName() + ", Path: " + fileNode.getFilePath();
    }
}
