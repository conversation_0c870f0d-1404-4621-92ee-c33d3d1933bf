package com.webank.maling.analyzer.java.strategy.impl;

import com.webank.maling.base.model.FieldNode;
import com.webank.maling.base.model.Node;
import com.webank.maling.analyzer.java.strategy.NodeContentStrategy;

/**
 * 字段节点内容获取策略
 * 
 * <AUTHOR> Assistant
 */
public class FieldNodeStrategy implements NodeContentStrategy {
    
    @Override
    public String getDigest(Node node, Object context) {
        if (!(node instanceof FieldNode fieldNode)) {
            throw new IllegalArgumentException("Node must be FieldNode");
        }

        return "Field: " + fieldNode.getName() + ", Type: " + fieldNode.getType();
    }
}
