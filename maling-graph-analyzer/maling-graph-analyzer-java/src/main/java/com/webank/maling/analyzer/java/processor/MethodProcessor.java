package com.webank.maling.analyzer.java.processor;

import com.webank.maling.analyzer.java.context.GraphContext;
import spoon.reflect.declaration.CtMethod;
import spoon.reflect.declaration.CtType;

import java.util.Map;

/**
 * 方法处理器
 */
public class MethodProcessor extends ExecutableProcessor<CtMethod<?>> {

    public MethodProcessor(GraphContext graphContext, Map<String, String> annotationValueToClass) {
        super(graphContext, annotationValueToClass);
    }


    @Override
    public void process(CtMethod element) {
        processExecutable(element);
    }

    @Override
    protected CtType<?> getDeclaringType(CtMethod<?> element) {
        return element.getDeclaringType();
    }

    @Override
    protected String getExecutableName(CtMethod<?> element, CtType<?> declaringType) {
        return element.getSimpleName();
    }

    @Override
    protected boolean isConstructor(CtMethod<?> element) {
        return false;
    }

    @Override
    protected boolean isStatic(CtMethod<?> element) {
        return element.isStatic();
    }

    @Override
    protected String determineVisibility(CtMethod<?> element) {
        String visibility = "default";
        if (element.isPublic()) {
            visibility = "public";
        } else if (element.isPrivate()) {
            visibility = "private";
        } else if (element.isProtected()) {
            visibility = "protected";
        }
        return visibility;
    }
}
