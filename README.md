# Java代码知识图谱构建工具

基于Spoon的Java代码知识图谱构建工具，将代码结构存储到NebulaGraph中，并使用向量模型生成向量数据存入Milvus。

## 功能特点

- 使用Spoon解析Java代码，提取代码结构和关系
- 将代码结构存储到NebulaGraph图数据库中
- 使用OpenAI API生成代码向量表示，并存储到Milvus向量数据库中
- 支持文件、类、方法、注解、注释等节点类型
- 支持包含、依赖、实例化、文档等边关系类型

## 系统要求

- Java 17或更高版本
- NebulaGraph 3.x
- Milvus 2.x
- OpenAI API密钥

## 快速开始

### 1. 配置

编辑`src/main/resources/application.properties`文件，配置NebulaGraph、Milvus和OpenAI：

```properties
# NebulaGraph配置
nebula.hosts=127.0.0.1:9669
nebula.username=root
nebula.password=nebula
nebula.space=java_graph
nebula.connection_pool_size=10
nebula.timeout=60000

# Milvus配置
milvus.host=127.0.0.1
milvus.port=19530
milvus.collection=java_graph_vectors
milvus.dimension=1536

# OpenAI配置
openai.model=text-embedding-3-small
openai.api_key=your_openai_api_key

# 项目配置
project.root_path=
project.id=java_graph
```

### 2. 构建

```bash
./gradlew build
```

### 3. 运行

```bash
./gradlew run
```

## 架构设计

### 节点类型

- 文件（file）：Java源文件
- 类（class）：类、接口、枚举、注解类型
- 函数（function）：方法、构造函数
- 注解（annotations）：注解实例
- 注释（comment）：代码注释

### 边关系

- 包含（contains）：表示一个节点包含另一个节点
- 依赖（depends_on）：表示一个节点依赖另一个节点
- 实例化（instance_of）：表示一个注解实例属于某个注解类型
- 文档（documented_by）：表示一个节点被某个注释文档化

## 使用场景

- 代码结构可视化
- 代码依赖分析
- 代码相似性搜索
- 代码智能问答

## 许可证

MIT
