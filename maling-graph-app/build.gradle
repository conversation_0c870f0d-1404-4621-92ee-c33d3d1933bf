plugins {
    id 'application'
}

description = 'Maling Graph App - 主应用程序'

application {
    mainClass = 'com.webank.maling.graph.app.Main'
}

dependencies {
    // 依赖所有模块
    implementation project(':maling-graph-base')
    implementation project(':maling-graph-repository')
    implementation project(':maling-graph-ai')
    implementation project(':maling-graph-analyzer:maling-graph-analyzer-java')
    implementation project(':maling-graph-documentation')
    
    // Lombok
    compileOnly "org.projectlombok:lombok:${lombokVersion}"
    annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
    
    // Logging
    implementation "ch.qos.logback:logback-classic:${logbackVersion}"
    
    // Test dependencies
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation "org.mockito:mockito-core:${mockitoVersion}"
}

// 创建可执行的fat jar
jar {
    manifest {
        attributes 'Main-Class': application.mainClass
    }
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
