package com.webank.maling.repository.documentation;

import com.webank.maling.base.entity.DocumentationTask;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 说明书生成任务数据访问接口
 * 
 * <AUTHOR> Graph Team
 */
public interface DocumentationTaskRepository {
    
    /**
     * 保存任务
     * 
     * @param task 任务实体
     * @return 保存后的实体
     */
    DocumentationTask save(DocumentationTask task);
    
    /**
     * 根据ID查找任务
     * 
     * @param id 任务ID
     * @return 任务实体
     */
    Optional<DocumentationTask> findById(Long id);
    
    /**
     * 根据入口点ID查找任务
     * 
     * @param entryPointId 入口点ID
     * @return 任务列表
     */
    List<DocumentationTask> findByEntryPointId(String entryPointId);
    
    /**
     * 根据入口点ID查找运行中的任务
     * 
     * @param entryPointId 入口点ID
     * @return 运行中的任务
     */
    Optional<DocumentationTask> findRunningTaskByEntryPointId(String entryPointId);
    
    /**
     * 根据状态查找任务
     * 
     * @param status 任务状态
     * @return 任务列表
     */
    List<DocumentationTask> findByStatus(DocumentationTask.TaskStatus status);
    
    /**
     * 查找运行中的任务
     * 
     * @return 运行中的任务列表
     */
    List<DocumentationTask> findRunningTasks();
    
    /**
     * 根据项目ID查找任务
     * 
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<DocumentationTask> findByProjectId(String projectId);
    
    /**
     * 查找指定时间之前创建的任务
     * 
     * @param createdBefore 时间点
     * @return 任务列表
     */
    List<DocumentationTask> findByCreatedAtBefore(LocalDateTime createdBefore);
    
    /**
     * 查找指定时间之前完成的任务
     * 
     * @param completedBefore 时间点
     * @return 任务列表
     */
    List<DocumentationTask> findByCompletedAtBefore(LocalDateTime completedBefore);
    
    /**
     * 更新任务
     * 
     * @param task 任务实体
     * @return 更新后的实体
     */
    DocumentationTask update(DocumentationTask task);
    
    /**
     * 删除任务
     * 
     * @param id 任务ID
     * @return 是否删除成功
     */
    boolean deleteById(Long id);
    
    /**
     * 批量删除任务
     * 
     * @param ids 任务ID列表
     * @return 删除的数量
     */
    int deleteByIds(List<Long> ids);
    
    /**
     * 删除指定时间之前完成的任务
     * 
     * @param completedBefore 时间点
     * @return 删除的数量
     */
    int deleteCompletedTasksBefore(LocalDateTime completedBefore);
    
    /**
     * 统计任务总数
     * 
     * @return 总数
     */
    long count();
    
    /**
     * 根据状态统计任务数量
     * 
     * @param status 任务状态
     * @return 数量
     */
    long countByStatus(DocumentationTask.TaskStatus status);
    
    /**
     * 统计运行中的任务数量
     * 
     * @return 数量
     */
    long countRunningTasks();
    
    /**
     * 根据项目ID统计任务数量
     * 
     * @param projectId 项目ID
     * @return 数量
     */
    long countByProjectId(String projectId);
    
    /**
     * 检查入口点是否有运行中的任务
     * 
     * @param entryPointId 入口点ID
     * @return 是否存在
     */
    boolean hasRunningTaskForEntryPoint(String entryPointId);
}
